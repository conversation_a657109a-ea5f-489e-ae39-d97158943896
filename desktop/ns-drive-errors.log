[ERROR] 2025/07/03 23:58:04 handlers.go:150: TraceID: d38e6dff-11c4-4d07-bb12-1bd961b0f480 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/03 23:58:04 handlers.go:154: TraceID: d38e6dff-11c4-4d07-bb12-1bd961b0f480 | Details: open : no such file or directory
[ERROR] 2025/07/03 23:58:04 handlers.go:150: TraceID: 7a350bd5-4c05-4701-bb3b-1badc41a93e9 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/03 23:58:04 handlers.go:154: TraceID: 7a350bd5-4c05-4701-bb3b-1badc41a93e9 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/03 23:58:04 handlers.go:159: TraceID: d38e6dff-11c4-4d07-bb12-1bd961b0f480 | Stack Trace:
goroutine 239 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.1/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x14000407770, 0x1400020e9a0, {0x1400047cb40?, 0x0?, 0x1400014e378?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x14000407770, {0x105ac8178?, 0x140003c6a50?}, {0x1400047cb40, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140000c09c0?, {0x105ac8178?, 0x140003c6a50?}, {0x1400047cb40?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140004dea80)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:136 +0x52c
desktop/backend.(*App).GetConfigInfo(0x140004dea80)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:164 +0x20c
reflect.Value.call({0x105a70080?, 0x140004dea80?, 0x14000619c78?}, {0x10544e99b, 0x4}, {0x10649b500, 0x0, 0x105063ac4?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x105a70080?, 0x140004dea80?, 0x106b4d188?}, {0x10649b500?, 0x140004b0000?, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000250d20, {0x105ad8b18, 0x1400017a510}, {0x10649b500, 0x0, 0x20?})
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 238
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/03 23:58:04 handlers.go:164: TraceID: d38e6dff-11c4-4d07-bb12-1bd961b0f480 | Underlying Error: open : no such file or directory
[ERROR] 2025/07/03 23:58:04 handlers.go:159: TraceID: 7a350bd5-4c05-4701-bb3b-1badc41a93e9 | Stack Trace:
goroutine 192 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.1/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x14000407770, 0x140004dc310, {0x1400016e320?, 0x0?, 0x140003f46f0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x14000407770, {0x105ac8178?, 0x140003ce3f0?}, {0x1400016e320, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140000c09c0?, {0x105ac8178?, 0x140003ce3f0?}, {0x1400016e320?, 0x14000268bf0?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140004dea80)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:136 +0x52c
desktop/backend.(*App).GetRemotes(0x140004dea80)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:201 +0x78
reflect.Value.call({0x105a70080?, 0x140004dea80?, 0x14000021c78?}, {0x10544e99b, 0x4}, {0x10649b500, 0x0, 0x105063ac4?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x105a70080?, 0x140004dea80?, 0x14000021ce8?}, {0x10649b500?, 0x10490df00?, 0x104b9edc0?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000250e60, {0x105ad8b18, 0x140003ae5a0}, {0x10649b500, 0x0, 0x20?})
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 191
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/03 23:58:04 handlers.go:164: TraceID: 7a350bd5-4c05-4701-bb3b-1badc41a93e9 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 00:07:51 handlers.go:150: TraceID: b09c20da-bdc4-4fb4-b055-66e828f9748f | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 00:07:51 handlers.go:154: TraceID: b09c20da-bdc4-4fb4-b055-66e828f9748f | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 00:07:51 handlers.go:159: TraceID: b09c20da-bdc4-4fb4-b055-66e828f9748f | Stack Trace:
goroutine 261 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.1/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400038a650, 0x14000692000, {0x1400013b540?, 0x0?, 0x14000482180?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400038a650, {0x1037f8198?, 0x14000304510?}, {0x1400013b540, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140001369c0?, {0x1037f8198?, 0x14000304510?}, {0x1400013b540?, 0x1400048a330?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140000d4000)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:137 +0x52c
desktop/backend.(*App).GetConfigInfo(0x140000d4000)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:164 +0x20c
reflect.Value.call({0x1037a00a0?, 0x140000d4000?, 0x1213?}, {0x10317e99b, 0x4}, {0x1041cb500, 0x0, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x1037a00a0?, 0x140000d4000?, 0x0?}, {0x1041cb500?, 0x0?, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x1400041a820, {0x103808b38, 0x14000600d50}, {0x1041cb500, 0x0, 0x20?})
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 260
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 00:07:51 handlers.go:164: TraceID: b09c20da-bdc4-4fb4-b055-66e828f9748f | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 00:08:05 handlers.go:150: TraceID: 2f43e475-a8c4-4ea9-ae29-a8160b347a34 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 00:08:05 handlers.go:154: TraceID: 2f43e475-a8c4-4ea9-ae29-a8160b347a34 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 00:08:05 handlers.go:150: TraceID: db399f95-0b40-409a-a107-e42d10802649 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 00:08:05 handlers.go:154: TraceID: db399f95-0b40-409a-a107-e42d10802649 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 00:08:05 handlers.go:159: TraceID: 2f43e475-a8c4-4ea9-ae29-a8160b347a34 | Stack Trace:
goroutine 264 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.1/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400028a010, 0x140004a6c40, {0x1400009c420?, 0x0?, 0x140003d0138?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400028a010, {0x101f84198?, 0x14000370a50?}, {0x1400009c420, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140001369c0?, {0x101f84198?, 0x14000370a50?}, {0x1400009c420?, 0x14000358bf0?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140002ba360)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:143 +0x530
desktop/backend.(*App).GetConfigInfo(0x140002ba360)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:164 +0x20c
reflect.Value.call({0x101f2c0a0?, 0x140002ba360?, 0x14000830c78?}, {0x10190aa5b, 0x4}, {0x102957500, 0x0, 0x10151fac4?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x101f2c0a0?, 0x140002ba360?, 0x0?}, {0x102957500?, 0x0?, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000492d20, {0x101f94b38, 0x14000320030}, {0x102957500, 0x0, 0x20?})
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 263
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 00:08:05 handlers.go:164: TraceID: 2f43e475-a8c4-4ea9-ae29-a8160b347a34 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 00:08:05 handlers.go:159: TraceID: db399f95-0b40-409a-a107-e42d10802649 | Stack Trace:
goroutine 313 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.1/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400028a010, 0x1400024c770, {0x140003063e0?, 0x0?, 0x1400039a120?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400028a010, {0x101f84198?, 0x14000384d80?}, {0x140003063e0, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140001369c0?, {0x101f84198?, 0x14000384d80?}, {0x140003063e0?, 0x1400071c420?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140002ba360)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:143 +0x530
desktop/backend.(*App).GetRemotes(0x140002ba360)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:201 +0x78
reflect.Value.call({0x101f2c0a0?, 0x140002ba360?, 0x14000580c78?}, {0x10190aa5b, 0x4}, {0x102957500, 0x0, 0x10151fac4?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x101f2c0a0?, 0x140002ba360?, 0x0?}, {0x102957500?, 0x0?, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000492dc0, {0x101f94b38, 0x14000341da0}, {0x102957500, 0x0, 0x20?})
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 312
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 00:08:05 handlers.go:164: TraceID: db399f95-0b40-409a-a107-e42d10802649 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 00:09:11 handlers.go:150: TraceID: 92105722-a60d-4c48-b3b1-db438a81a76b | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 00:09:11 handlers.go:154: TraceID: 92105722-a60d-4c48-b3b1-db438a81a76b | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 00:09:11 handlers.go:150: TraceID: c8f82710-4588-4c29-974b-30292e05f469 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 00:09:11 handlers.go:154: TraceID: c8f82710-4588-4c29-974b-30292e05f469 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 00:09:11 handlers.go:159: TraceID: 92105722-a60d-4c48-b3b1-db438a81a76b | Stack Trace:
goroutine 301 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.1/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400011a1c0, 0x1400020e1c0, {0x14000362000?, 0x8?, 0x140003e8300?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400011a1c0, {0x105aa4198?, 0x140003864e0?}, {0x14000362000, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140000c49c0?, {0x105aa4198?, 0x140003864e0?}, {0x14000362000?, 0x140004407d0?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x1400014a1b0)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:143 +0x530
desktop/backend.(*App).GetConfigInfo(0x1400014a1b0)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x105a4c0a0?, 0x1400014a1b0?, 0x1400062ac78?}, {0x10542a37b, 0x4}, {0x106477500, 0x0, 0x10503fac4?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x105a4c0a0?, 0x1400014a1b0?, 0x0?}, {0x106477500?, 0x0?, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x140004cae60, {0x105ab4b38, 0x14000202a50}, {0x106477500, 0x0, 0x20?})
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 300
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 00:09:11 handlers.go:164: TraceID: 92105722-a60d-4c48-b3b1-db438a81a76b | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 00:09:11 handlers.go:159: TraceID: c8f82710-4588-4c29-974b-30292e05f469 | Stack Trace:
goroutine 335 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.1/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400011a1c0, 0x14000250070, {0x1400012e5e0?, 0x0?, 0x14000342150?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400011a1c0, {0x105aa4198?, 0x140003a8390?}, {0x1400012e5e0, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140000c49c0?, {0x105aa4198?, 0x140003a8390?}, {0x1400012e5e0?, 0x140004407d0?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x1400014a1b0)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:143 +0x530
desktop/backend.(*App).GetRemotes(0x0?)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:184 +0x24
reflect.Value.call({0x105a4c0a0?, 0x1400014a1b0?, 0x14000526c78?}, {0x10542a37b, 0x4}, {0x106477500, 0x0, 0x10503fac4?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x105a4c0a0?, 0x1400014a1b0?, 0x104b51670?}, {0x106477500?, 0x14000526d38?, 0x1048c7214?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x140004caf00, {0x105ab4b38, 0x14000369320}, {0x106477500, 0x0, 0x20?})
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 334
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 00:09:11 handlers.go:164: TraceID: c8f82710-4588-4c29-974b-30292e05f469 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 00:09:26 handlers.go:150: TraceID: 38e9b768-1468-4771-ae26-180c7e341051 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 00:09:26 handlers.go:154: TraceID: 38e9b768-1468-4771-ae26-180c7e341051 | Details: open : no such file or directory
[ERROR] 2025/07/04 00:09:26 handlers.go:150: TraceID: 9c6ce759-3438-401b-84f5-350cbb4d463b | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 00:09:26 handlers.go:154: TraceID: 9c6ce759-3438-401b-84f5-350cbb4d463b | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 00:09:26 handlers.go:159: TraceID: 9c6ce759-3438-401b-84f5-350cbb4d463b | Stack Trace:
goroutine 376 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.1/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x14000112ff0, 0x140002bc000, {0x1400009c000?, 0x8?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x14000112ff0, {0x105b6c198?, 0x1400033c150?}, {0x1400009c000, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140000ec490?, {0x105b6c198?, 0x1400033c150?}, {0x1400009c000?, 0x140004325b0?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140000ec480)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetRemotes(0x0?)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:184 +0x24
reflect.Value.call({0x105b140a0?, 0x140000ec480?, 0x140005b0c78?}, {0x1054f5c5b, 0x4}, {0x10653f500, 0x0, 0x10510bac4?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x105b140a0?, 0x140000ec480?, 0x0?}, {0x10653f500?, 0x0?, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x1400061e820, {0x105b7cb38, 0x14000287b30}, {0x10653f500, 0x0, 0x20?})
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 375
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 00:09:26 handlers.go:164: TraceID: 9c6ce759-3438-401b-84f5-350cbb4d463b | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 00:09:26 handlers.go:159: TraceID: 38e9b768-1468-4771-ae26-180c7e341051 | Stack Trace:
goroutine 93 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.1/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x14000112ff0, 0x1400024e070, {0x14000324160?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x14000112ff0, {0x105b6c198?, 0x1400035c1b0?}, {0x14000324160, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140000ec490?, {0x105b6c198?, 0x1400035c1b0?}, {0x14000324160?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140000ec480)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x140000ec480)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x105b140a0?, 0x140000ec480?, 0x140000e7c78?}, {0x1054f5c5b, 0x4}, {0x10653f500, 0x0, 0x10510bac4?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x105b140a0?, 0x140000ec480?, 0x104c1d670?}, {0x10653f500?, 0x140000e7d38?, 0x104993214?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x1400061e780, {0x105b7cb38, 0x14000348240}, {0x10653f500, 0x0, 0x1?})
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 331
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 00:09:26 handlers.go:164: TraceID: 38e9b768-1468-4771-ae26-180c7e341051 | Underlying Error: open : no such file or directory
[ERROR] 2025/07/04 00:09:55 handlers.go:150: TraceID: 4bec31f1-cfc7-4813-8992-55181ec0b52f | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 00:09:55 handlers.go:154: TraceID: 4bec31f1-cfc7-4813-8992-55181ec0b52f | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 00:09:55 handlers.go:159: TraceID: 4bec31f1-cfc7-4813-8992-55181ec0b52f | Stack Trace:
goroutine 234 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.1/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400003d530, 0x1400020caf0, {0x1400024a4e0?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400003d530, {0x10171c198?, 0x140003d95c0?}, {0x1400024a4e0, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140000d2400?, {0x10171c198?, 0x140003d95c0?}, {0x1400024a4e0?, 0x14000010550?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140000d23f0)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetRemotes(0x0?)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:184 +0x24
reflect.Value.call({0x1016c40a0?, 0x140000d23f0?, 0x14000092c78?}, {0x1010a57bb, 0x4}, {0x1020ef500, 0x0, 0x100cbb634?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x1016c40a0?, 0x140000d23f0?, 0x1007cd670?}, {0x1020ef500?, 0x14000092d38?, 0x100543214?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x140001fcfa0, {0x10172cb38, 0x14000387140}, {0x1020ef500, 0x0, 0x20?})
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 233
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 00:09:55 handlers.go:164: TraceID: 4bec31f1-cfc7-4813-8992-55181ec0b52f | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 00:09:55 handlers.go:150: TraceID: f8c5fb8e-9c80-42cb-98e8-2cf61e239570 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 00:09:55 handlers.go:154: TraceID: f8c5fb8e-9c80-42cb-98e8-2cf61e239570 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 00:09:55 handlers.go:159: TraceID: f8c5fb8e-9c80-42cb-98e8-2cf61e239570 | Stack Trace:
goroutine 349 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.1/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400003d530, 0x1400020c000, {0x1400027e020?, 0x8?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400003d530, {0x10171c198?, 0x140003b05d0?}, {0x1400027e020, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140000d2400?, {0x10171c198?, 0x140003b05d0?}, {0x1400027e020?, 0x14000124e90?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140000d23f0)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x140000d23f0)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x1016c40a0?, 0x140000d23f0?, 0x1213?}, {0x1010a57bb, 0x4}, {0x1020ef500, 0x0, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x1016c40a0?, 0x140000d23f0?, 0x0?}, {0x1020ef500?, 0x10?, 0x1025cd8d0?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x140001fce60, {0x10172cb38, 0x140003c8ed0}, {0x1020ef500, 0x0, 0x20?})
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 348
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 00:09:55 handlers.go:164: TraceID: f8c5fb8e-9c80-42cb-98e8-2cf61e239570 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 00:13:28 handlers.go:150: TraceID: d27a51dd-e713-4473-bca6-c0edecca58fd | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 00:13:28 handlers.go:154: TraceID: d27a51dd-e713-4473-bca6-c0edecca58fd | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 00:13:28 handlers.go:150: TraceID: 8735e030-22d3-413a-916e-b3efb73844da | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 00:13:28 handlers.go:154: TraceID: 8735e030-22d3-413a-916e-b3efb73844da | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 00:13:28 handlers.go:159: TraceID: 8735e030-22d3-413a-916e-b3efb73844da | Stack Trace:
goroutine 287 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.1/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x14000516010, 0x14000250000, {0x140002868a0?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x14000516010, {0x105b58198?, 0x140002e82a0?}, {0x140002868a0, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x14000518010?, {0x105b58198?, 0x140002e82a0?}, {0x140002868a0?, 0x140003d2140?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x14000518000)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x14000518000)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x105b000a0?, 0x14000518000?, 0x1213?}, {0x1054e17bb, 0x4}, {0x10652b500, 0x0, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x105b000a0?, 0x14000518000?, 0x0?}, {0x10652b500?, 0x0?, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x1400043af00, {0x105b68b38, 0x140002d01e0}, {0x10652b500, 0x0, 0x20?})
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 286
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 00:13:28 handlers.go:164: TraceID: 8735e030-22d3-413a-916e-b3efb73844da | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 00:13:28 handlers.go:159: TraceID: d27a51dd-e713-4473-bca6-c0edecca58fd | Stack Trace:
goroutine 323 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.1/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x14000516010, 0x1400023e000, {0x1400009d3e0?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x14000516010, {0x105b58198?, 0x140002de510?}, {0x1400009d3e0, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x14000518010?, {0x105b58198?, 0x140002de510?}, {0x1400009d3e0?, 0x1400026a140?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x14000518000)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetRemotes(0x0?)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:184 +0x24
reflect.Value.call({0x105b000a0?, 0x14000518000?, 0x1613?}, {0x1054e17bb, 0x4}, {0x10652b500, 0x0, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x105b000a0?, 0x14000518000?, 0x10140004e1ce8?}, {0x10652b500?, 0x14000306048?, 0x105882ca0?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x1400043afa0, {0x105b68b38, 0x140002bc5a0}, {0x10652b500, 0x0, 0x20?})
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 322
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 00:13:28 handlers.go:164: TraceID: d27a51dd-e713-4473-bca6-c0edecca58fd | Underlying Error: open .config/.profiles: no such file or directory
